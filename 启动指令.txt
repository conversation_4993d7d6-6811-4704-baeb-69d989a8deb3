python "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\020-语言互译-修复版.py"

python "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\api_key_tool.py"

未加密
AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA（自用）

AIzaSyCCxPRWSzBm-EDHbkIJ5nxaiPuXmycWU40

加密后（自用）
Dagz06vVJ3C2LuWQxT7PikT+3cM+jgYbSywIelTnHOkjoUeICyXYG/gCyeoi7nMgHBfByLpJMwkjw+U2shLIJ3IJNA==

p6FG9jO4s8Ljs6dpj4Mj9TEMHNJNh6o2+vhoQLcejbwYn8YQa6UTLxs5YSB4956AFjSpIMBnLxQRk4Zgpx7bMxVE1w==

python -m PyInstaller --clean --noconfirm --onefile `
  --add-data "config.yaml;." `
  --add-data "mode_config.yaml;." `
  --add-data "api_crypto.py;." `
  --add-data "config_management.py;." `
  --icon "图标.ico" `
  --name "多语言互译器2.1.0" `
  --distpath "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译" `
  "语言互译.py"


# API加解密工具的打包命令（移除--console选项，使用相同的图标处理方式）
python -m PyInstaller --clean --noconfirm --onefile `
  --add-data "api_crypto.py;." `
  --icon "图标.ico" `
  --name "API加解密" `
  --distpath "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译" `
  "api_key_tool.py"


